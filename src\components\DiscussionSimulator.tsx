import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CharacterAvatar } from './CharacterAvatar';
import { VoiceControls } from './VoiceControls';
import { ConversationDisplay } from './ConversationDisplay';
import { TopicSelector } from './TopicSelector';
import { useElevenLabsConversation } from '@/hooks/useElevenLabsConversation';
import { CHARACTERS, USER_CHARACTER } from '@/types/conversation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FiSettings, FiPlay, FiPause, FiSave, FiMic, FiMicOff } from 'react-icons/fi';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

export const DiscussionSimulator = () => {
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [showTopicSelector, setShowTopicSelector] = useState(true);
  const [apiKey, setApiKey] = useState<string>('');
  const [tempApiKey, setTempApiKey] = useState<string>('');
  const [showSettings, setShowSettings] = useState(false);
  const { toast } = useToast();

  // Load saved API key on mount
  useEffect(() => {
    const saved = localStorage.getItem('elevenlab-api-key');
    if (saved) {
      setApiKey(saved);
      setTempApiKey(saved);
    }
  }, []);

  const {
    state,
    isRecording,
    isMuted,
    hasPermission,
    isValidApiKey,
    startConversation,
    stopConversation,
    toggleRecording,
    toggleMute,
    requestMicrophonePermission,
    conversationStatus,
    isSpeaking
  } = useElevenLabsConversation({ 
    topic: selectedTopic,
    apiKey 
  });

  const handleSaveApiKey = () => {
    if (tempApiKey.trim()) {
      setApiKey(tempApiKey.trim());
      localStorage.setItem('elevenlab-api-key', tempApiKey.trim());
      toast({
        title: "API Key Saved",
        description: "Your ElevenLabs API key has been saved successfully."
      });
    } else {
      toast({
        title: "Invalid Input",
        description: "Please enter a valid API key.",
        variant: "destructive"
      });
    }
  };

  const handleClearApiKey = () => {
    setApiKey('');
    setTempApiKey('');
    localStorage.removeItem('elevenlab-api-key');
    toast({
      title: "API Key Cleared",
      description: "Your API key has been removed."
    });
  };

  const handleTopicSelect = (topic: string) => {
    setSelectedTopic(topic);
    setShowTopicSelector(false);
  };

  const handleNewTopic = () => {
    stopConversation();
    setShowTopicSelector(true);
  };

  if (showTopicSelector) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-background via-surface to-background">
        <TopicSelector
          isOpen={showTopicSelector}
          onClose={() => setShowTopicSelector(false)}
          onSelectTopic={handleTopicSelect}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface to-background">
      {/* Header */}
      <motion.header 
        className="p-6 border-b border-border/50"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gradient-alex">AI Discussion Simulator</h1>
            <p className="text-muted-foreground">Real-time voice conversation with AI characters</p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Connection Status */}
            <Badge 
              variant={state.isConnected ? "default" : "secondary"}
              className={cn(
                "neumorphic flex items-center gap-2",
                state.isConnected ? "bg-green-500/20 text-green-400" : "bg-gray-500/20 text-gray-400"
              )}
            >
              <div className={cn(
                "w-2 h-2 rounded-full",
                state.isConnected ? "bg-green-400 animate-pulse" : "bg-gray-400"
              )} />
              {state.isConnected ? "Connected" : "Disconnected"}
            </Badge>

            {/* API Key Status */}
            <Badge 
              variant={isValidApiKey ? "default" : "destructive"}
              className="neumorphic"
            >
              {apiKey ? (isValidApiKey ? "API Key Valid" : "Invalid Key") : "No API Key"}
            </Badge>

            {/* Microphone Status */}
            <Badge 
              variant={hasPermission ? "default" : "secondary"}
              className="neumorphic flex items-center gap-2"
            >
              {hasPermission ? <FiMic className="w-3 h-3" /> : <FiMicOff className="w-3 h-3" />}
              {hasPermission ? "Mic Ready" : "Mic Needed"}
            </Badge>
            
            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowSettings(!showSettings)}
              className="neumorphic"
            >
              <FiSettings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </motion.header>

      {/* Settings Panel */}
      {showSettings && (
        <motion.div
          className="p-6 border-b border-border/50 bg-surface/50"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
        >
          <div className="max-w-7xl mx-auto">
            <h3 className="text-lg font-semibold mb-4 text-gradient-alex">Settings</h3>
            <div className="space-y-6">
              {/* API Key Section */}
              <div className="space-y-3">
                <Label htmlFor="api-key" className="text-sm font-medium">
                  ElevenLabs API Key
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="api-key"
                    type="password"
                    value={tempApiKey}
                    onChange={(e) => setTempApiKey(e.target.value)}
                    placeholder="Enter your ElevenLabs API key (sk-...)"
                    className="neumorphic-sunken border-0 flex-1"
                  />
                  <Button
                    onClick={handleSaveApiKey}
                    disabled={!tempApiKey.trim()}
                    className="neumorphic bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                  >
                    <FiSave className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                  {apiKey && (
                    <Button
                      onClick={handleClearApiKey}
                      variant="destructive"
                      className="neumorphic"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Get your API key from{' '}
                  <a 
                    href="https://elevenlabs.io" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:underline"
                  >
                    elevenlabs.io
                  </a>
                </p>
              </div>

              {/* Microphone Section */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Microphone Access</Label>
                <div className="flex items-center gap-4">
                  <Badge 
                    variant={hasPermission ? "default" : "secondary"}
                    className="neumorphic"
                  >
                    {hasPermission ? "✓ Microphone Ready" : "⚠ Permission Needed"}
                  </Badge>
                  {!hasPermission && (
                    <Button
                      onClick={requestMicrophonePermission}
                      variant="outline"
                      size="sm"
                      className="neumorphic"
                    >
                      Request Access
                    </Button>
                  )}
                </div>
              </div>

              {/* Other Controls */}
              <div className="flex gap-4">
                <Button
                  onClick={handleNewTopic}
                  variant="outline"
                  className="neumorphic"
                >
                  Change Topic
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      <div className="max-w-7xl mx-auto p-6">
        {/* Topic Display */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h2 className="text-xl font-semibold mb-2">Discussion Topic</h2>
          <Badge variant="outline" className="text-lg p-3 neumorphic">
            {selectedTopic}
          </Badge>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Participants Panel */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="p-6 neumorphic border-border/30">
              <h3 className="font-semibold mb-6 text-center">Participants</h3>
              
              <div className="space-y-6">
                {/* AI Characters */}
                {Object.values(CHARACTERS).map((character, index) => (
                  <motion.div
                    key={character.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  >
                    <CharacterAvatar
                      character={character}
                      isSpeaking={state.currentSpeaker === character.id && isSpeaking}
                      isListening={state.isConnected && !isSpeaking}
                      size="md"
                      className="w-full"
                    />
                  </motion.div>
                ))}

                {/* User */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.7 }}
                  className="pt-4 border-t border-border/30"
                >
                  <CharacterAvatar
                    character={USER_CHARACTER}
                    isSpeaking={isRecording && !isMuted}
                    isListening={state.isConnected}
                    size="md"
                    className="w-full"
                  />
                </motion.div>
              </div>

              {/* Connection Controls */}
              <div className="mt-8 space-y-4">
                {!state.isConnected ? (
                  <Button
                    onClick={startConversation}
                    disabled={!apiKey || !isValidApiKey}
                    className={cn(
                      "w-full neumorphic-elevated text-white hover:shadow-xl transition-all duration-300",
                      apiKey && isValidApiKey 
                        ? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500" 
                        : "bg-gray-500 cursor-not-allowed opacity-50"
                    )}
                  >
                    <FiPlay className="w-4 h-4 mr-2" />
                    Start Discussion
                  </Button>
                ) : (
                  <Button
                    onClick={stopConversation}
                    variant="destructive"
                    className="w-full neumorphic bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-xl"
                  >
                    <FiPause className="w-4 h-4 mr-2" />
                    End Discussion
                  </Button>
                )}
                
                <div className="space-y-2 text-xs text-muted-foreground text-center">
                  {!apiKey && (
                    <p className="text-yellow-400">⚠ API key required</p>
                  )}
                  {apiKey && !isValidApiKey && (
                    <p className="text-red-400">⚠ Invalid API key format</p>
                  )}
                  {!hasPermission && (
                    <p className="text-orange-400">⚠ Microphone access needed</p>
                  )}
                  {apiKey && isValidApiKey && hasPermission && (
                    <p className="text-green-400">✓ Ready to start!</p>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Conversation Display */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="h-[600px] neumorphic border-border/30 overflow-hidden">
              <ConversationDisplay
                messages={state.messages}
                currentSpeaker={state.currentSpeaker}
              />
            </Card>
          </motion.div>
        </div>

        {/* Voice Controls */}
        <motion.div
          className="mt-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Card className="neumorphic border-border/30">
            <VoiceControls
              isRecording={isRecording}
              isMuted={isMuted}
              isPlaying={isSpeaking}
              onToggleRecording={toggleRecording}
              onToggleMute={toggleMute}
              onStop={stopConversation}
              disabled={!state.isConnected}
            />
          </Card>
        </motion.div>
      </div>
    </div>
  );
};