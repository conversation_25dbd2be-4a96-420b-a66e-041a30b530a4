import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { FaMicrophone, FaMicrophoneSlash, FaPlay, FaPause, FaStop } from 'react-icons/fa';
import { cn } from '@/lib/utils';

interface VoiceControlsProps {
  isRecording: boolean;
  isMuted: boolean;
  isPlaying: boolean;
  onToggleRecording: () => void;
  onToggleMute: () => void;
  onStop: () => void;
  disabled?: boolean;
}

export const VoiceControls = ({
  isRecording,
  isMuted,
  isPlaying,
  onToggleRecording,
  onToggleMute,
  onStop,
  disabled = false
}: VoiceControlsProps) => {
  return (
    <div className="flex items-center justify-center gap-4 p-6">
      {/* Mute/Unmute Button */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant="outline"
          size="icon"
          onClick={onToggleMute}
          disabled={disabled}
          className={cn(
            'w-12 h-12 rounded-full neumorphic transition-all duration-300',
            {
              'bg-destructive/20 text-destructive hover:bg-destructive/30': isMuted,
              'hover:shadow-lg': !disabled,
            }
          )}
        >
          {isMuted ? <FaMicrophoneSlash className="w-5 h-5" /> : <FaMicrophone className="w-5 h-5" />}
        </Button>
      </motion.div>

      {/* Main Recording Button */}
      <motion.div 
        whileHover={{ scale: 1.05 }} 
        whileTap={{ scale: 0.95 }}
        className="relative"
      >
        <Button
          onClick={onToggleRecording}
          disabled={disabled || isMuted}
          className={cn(
            'w-20 h-20 rounded-full font-semibold text-lg transition-all duration-300',
            'neumorphic-elevated hover:shadow-xl',
            {
              'bg-gradient-to-r from-red-500 to-red-600 text-white animate-speaking-pulse': isRecording,
              'bg-gradient-to-r from-green-500 to-green-600 text-white': !isRecording && !disabled,
              'opacity-50 cursor-not-allowed': disabled || isMuted,
            }
          )}
        >
          {isRecording ? (
            <FaStop className="w-6 h-6" />
          ) : (
            <FaMicrophone className="w-6 h-6" />
          )}
        </Button>
        
        {/* Recording indicator */}
        {isRecording && (
          <motion.div
            className="absolute -inset-2 rounded-full border-2 border-red-500 opacity-50"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "easeOut"
            }}
          />
        )}
      </motion.div>

      {/* Stop All Button */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant="outline"
          size="icon"
          onClick={onStop}
          disabled={disabled}
          className={cn(
            'w-12 h-12 rounded-full neumorphic transition-all duration-300',
            'hover:bg-destructive/20 hover:text-destructive hover:shadow-lg',
            { 'opacity-50 cursor-not-allowed': disabled }
          )}
        >
          <FaStop className="w-5 h-5" />
        </Button>
      </motion.div>
    </div>
  );
};