# AI Voice Discussion Simulator

A real-time voice-based group discussion simulator using ElevenLabs Conversational AI. Experience dynamic conversations with three distinct AI characters through voice interaction.

![AI Discussion Simulator](https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop)

## 🌟 Features

- **Real-time Voice Conversation**: Talk directly with AI characters using your microphone
- **Three Distinct AI Personalities**:
  - **<PERSON>** (Optimistic Leader): Enthusiastic and encouraging
  - **<PERSON>** (Skeptical Analyst): Critical thinker who asks tough questions
  - **<PERSON>** (Creative Visionary): Imaginative and innovative
- **Beautiful Dark Neumorphic Design**: Modern UI with smooth animations
- **Topic-Based Discussions**: Choose from suggested topics or create your own
- **Visual Voice Indicators**: Avatars pulse and animate during speech
- **Real-time Transcription**: See conversation messages in real-time
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or later)
- ElevenLabs API key ([Get one here](https://elevenlabs.io))

### Installation

```bash
# Clone the repository
git clone <YOUR_GIT_URL>
cd ai-voice-discussion-simulator

# Install dependencies
npm install

# Start the development server
npm run dev
```

The app will be available at `http://localhost:8080`

### Setup

1. **Get ElevenLabs API Key**:
   - Sign up at [ElevenLabs](https://elevenlabs.io)
   - Navigate to your profile settings to get your API key

2. **Configure the App**:
   - Click the settings icon in the top right
   - Enter your ElevenLabs API key
   - The key is stored locally and not sent to any external services

3. **Start a Discussion**:
   - Choose a topic from the suggestions or create your own
   - Click "Start Discussion" 
   - Allow microphone access when prompted
   - Start talking! The AI characters will respond in turn

## 🎭 AI Characters

### Alex - The Optimistic Leader 🌟
- **Voice**: Sarah (ElevenLabs)
- **Personality**: Enthusiastic, encouraging, sees opportunities in challenges
- **Role**: Motivates the group and keeps discussions positive

### Jordan - The Skeptical Analyst 🔍  
- **Voice**: Roger (ElevenLabs)
- **Personality**: Critical thinker, asks tough questions, data-driven
- **Role**: Challenges assumptions and demands evidence

### Taylor - The Creative Visionary 🎨
- **Voice**: Charlotte (ElevenLabs)  
- **Personality**: Imaginative, thinks outside the box, innovative solutions
- **Role**: Brings fresh perspectives and creative approaches

## 🎨 Design Features

- **Dark Neumorphic Theme**: Soft shadows and elevated surfaces
- **Framer Motion Animations**: Smooth transitions and interactions
- **Character-Specific Colors**: Each participant has unique visual identity
- **Voice Activity Indicators**: Real-time visual feedback during speech
- **Responsive Layout**: Adapts to different screen sizes

## 🛠 Technical Stack

- **Frontend**: React, TypeScript, Vite
- **Styling**: Tailwind CSS, Framer Motion
- **Voice AI**: ElevenLabs Conversational AI
- **UI Components**: shadcn/ui
- **Icons**: React Icons

## 🔧 Configuration

### ElevenLabs Integration

The app uses the ElevenLabs Conversational AI API with the following configuration:

```typescript
// Character voice mappings
const CHARACTERS = {
  alex: { voiceId: 'EXAVITQu4vr4xnSDxMaL' },  // Sarah
  jordan: { voiceId: 'CwhRBWXzGAHq8TQ4Fs17' }, // Roger
  taylor: { voiceId: 'XB0fDUnXU5powFXDhCwa' }  // Charlotte
}
```

### Customization

You can customize the characters, topics, and conversation flow by modifying:
- `src/types/conversation.ts` - Character definitions
- `src/components/TopicSelector.tsx` - Available topics
- `src/hooks/useElevenLabsConversation.ts` - Conversation logic

## 📱 Usage

1. **Select a Topic**: Choose from predefined topics or enter your own
2. **Connect**: Enter your ElevenLabs API key and start the discussion
3. **Talk**: Press and hold the microphone button to speak
4. **Listen**: AI characters will respond with unique perspectives
5. **Continue**: Keep the conversation flowing naturally

## 🔊 Voice Controls

- **🎤 Microphone Button**: Press to speak (large center button)
- **🔇 Mute Button**: Toggle microphone on/off
- **⏹ Stop Button**: End the current discussion

## 🎯 Example Topics

- The Future of Artificial Intelligence
- Climate Change Solutions  
- Remote Work vs Office Culture
- Social Media's Impact on Society
- The Ethics of Gene Editing
- Space Exploration Priorities
- Universal Basic Income
- Education in the Digital Age

## 🚨 Troubleshooting

### Common Issues

**Microphone not working?**
- Ensure browser permissions are granted for microphone access
- Check that your microphone is properly connected
- Try refreshing the page and granting permissions again

**AI not responding?**
- Verify your ElevenLabs API key is correct
- Check your internet connection
- Ensure you have sufficient API credits

**Audio quality issues?**
- Check your microphone settings
- Ensure stable internet connection
- Try using headphones to prevent feedback

## 📄 License

This project is built with Lovable and is available for personal and educational use.

## 🤝 Contributing

This is a Lovable-generated project. To modify:
1. Use the Lovable editor for the best experience
2. Or clone locally and push changes back to the repo
3. All changes sync automatically with the Lovable project

## 🔗 Links

- [ElevenLabs API Documentation](https://docs.elevenlabs.io)
- [Lovable Platform](https://lovable.dev)
- [React Documentation](https://react.dev)
- [Tailwind CSS](https://tailwindcss.com)

---

**Ready to start your AI discussion? Launch the app and let the conversation begin! 🚀**