import { motion, AnimatePresence } from 'framer-motion';
import { ConversationMessage } from '@/types/conversation';
import { CharacterAvatar } from './CharacterAvatar';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useEffect, useRef } from 'react';

interface ConversationDisplayProps {
  messages: ConversationMessage[];
  currentSpeaker?: string | null;
  className?: string;
}

export const ConversationDisplay = ({ 
  messages, 
  currentSpeaker, 
  className 
}: ConversationDisplayProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className={cn('flex flex-col h-full', className)}>
      <div className="p-6 border-b border-border/50">
        <h2 className="text-xl font-bold text-gradient-alex">Discussion</h2>
        <p className="text-sm text-muted-foreground">
          Real-time voice conversation • {messages.length} messages
        </p>
      </div>

      <ScrollArea className="flex-1 p-6">
        <div className="space-y-6">
          <AnimatePresence mode="popLayout">
            {messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.05,
                  ease: "easeOut"
                }}
                className={cn(
                  'flex gap-4 group',
                  message.isUser ? 'flex-row-reverse' : 'flex-row'
                )}
              >
                <CharacterAvatar
                  character={message.character}
                  isSpeaking={currentSpeaker === message.character.id}
                  size="sm"
                  className="flex-shrink-0"
                />
                
                <motion.div
                  className={cn(
                    'flex-1 max-w-[80%]',
                    message.isUser ? 'text-right' : 'text-left'
                  )}
                  whileHover={{ scale: 1.01 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className={cn(
                    'inline-block p-4 rounded-2xl neumorphic',
                    'backdrop-blur-sm border border-border/30',
                    message.isUser ? 'animate-slide-in-right' : 'animate-slide-in-left'
                  )}>
                    <div className="flex items-center gap-2 mb-2">
                      <span className={cn(
                        'font-semibold text-sm',
                        `text-gradient-${message.character.color}`
                      )}>
                        {message.character.name}
                      </span>
                      <span className="text-xs text-muted-foreground opacity-60">
                        {message.timestamp.toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    </div>
                    
                    <p className="text-sm leading-relaxed text-foreground">
                      {message.content}
                    </p>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {/* Typing indicator */}
          {currentSpeaker && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center gap-3 text-muted-foreground"
            >
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-current rounded-full opacity-40"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.4, 0.8, 0.4]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut"
                    }}
                  />
                ))}
              </div>
              <span className="text-sm">AI is speaking...</span>
            </motion.div>
          )}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>
    </div>
  );
};