import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface TopicSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTopic: (topic: string) => void;
}

const SUGGESTED_TOPICS = [
  "The Future of Artificial Intelligence",
  "Climate Change Solutions",
  "Remote Work vs Office Culture", 
  "Social Media's Impact on Society",
  "The Ethics of Gene Editing",
  "Space Exploration Priorities",
  "Universal Basic Income",
  "The Role of Education in the Digital Age",
  "Sustainable Energy Transition",
  "Privacy in the Digital World"
];

export const TopicSelector = ({ isOpen, onClose, onSelectTopic }: TopicSelectorProps) => {
  const [customTopic, setCustomTopic] = useState('');

  const handleSelectTopic = (topic: string) => {
    onSelectTopic(topic);
    onClose();
  };

  const handleCustomSubmit = () => {
    if (customTopic.trim()) {
      handleSelectTopic(customTopic);
      setCustomTopic('');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="neumorphic border-border/50 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gradient-alex">
            Choose Discussion Topic
          </DialogTitle>
          <p className="text-muted-foreground">
            Select a topic for your AI group discussion or create your own
          </p>
        </DialogHeader>

        <div className="space-y-6 py-6">
          {/* Custom Topic Input */}
          <div className="space-y-3">
            <Label htmlFor="custom-topic" className="text-sm font-medium">
              Custom Topic
            </Label>
            <div className="flex gap-2">
              <Input
                id="custom-topic"
                value={customTopic}
                onChange={(e) => setCustomTopic(e.target.value)}
                placeholder="Enter your discussion topic..."
                className="neumorphic-sunken border-0 focus-visible:ring-2 focus-visible:ring-primary"
                onKeyDown={(e) => e.key === 'Enter' && handleCustomSubmit()}
              />
              <Button 
                onClick={handleCustomSubmit}
                disabled={!customTopic.trim()}
                className="neumorphic hover:neumorphic-elevated"
              >
                Start
              </Button>
            </div>
          </div>

          {/* Suggested Topics */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Suggested Topics
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
              {SUGGESTED_TOPICS.map((topic, index) => (
                <motion.div
                  key={topic}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Badge
                    variant="outline"
                    className={`
                      w-full p-3 text-left cursor-pointer transition-all duration-200 
                      neumorphic hover:neumorphic-elevated hover:scale-[1.02] 
                      border-border/30 text-foreground justify-start
                    `}
                    onClick={() => handleSelectTopic(topic)}
                  >
                    {topic}
                  </Badge>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};