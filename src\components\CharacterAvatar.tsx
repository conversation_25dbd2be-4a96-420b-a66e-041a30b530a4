import { motion } from 'framer-motion';
import { Character } from '@/types/conversation';
import { cn } from '@/lib/utils';

interface CharacterAvatarProps {
  character: Character;
  isSpeaking?: boolean;
  isListening?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CharacterAvatar = ({ 
  character, 
  isSpeaking = false, 
  isListening = false,
  size = 'md',
  className 
}: CharacterAvatarProps) => {
  const sizeClasses = {
    sm: 'w-12 h-12 text-xl',
    md: 'w-16 h-16 text-2xl',
    lg: 'w-24 h-24 text-4xl'
  };

  return (
    <div className={cn('relative flex flex-col items-center gap-2', className)}>
      <motion.div
        className={cn(
          'relative rounded-full flex items-center justify-center font-bold text-background transition-all duration-300',
          sizeClasses[size],
          `avatar-${character.color}`,
          {
            'animate-speaking-pulse': isSpeaking,
            'animate-listening-pulse': isListening && !isSpeaking,
          }
        )}
        animate={isSpeaking ? { scale: [1, 1.1, 1] } : { scale: 1 }}
        transition={{
          duration: 0.6,
          repeat: isSpeaking ? Infinity : 0,
          ease: "easeInOut"
        }}
      >
        <span className="relative z-10">{character.avatar}</span>
        
        {/* Voice waves indicator */}
        {isSpeaking && (
          <div className="absolute -inset-2 flex items-center justify-center">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-full h-full rounded-full border-2 border-current opacity-30"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.3, 0, 0.3]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>
        )}
        
        {/* Listening pulse */}
        {isListening && !isSpeaking && (
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-current opacity-20"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.5, 0.2]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </motion.div>
      
      <div className="text-center">
        <h3 className={cn(
          'font-semibold text-sm',
          `text-gradient-${character.color}`
        )}>
          {character.name}
        </h3>
        <p className="text-xs text-muted-foreground opacity-80">
          {character.role}
        </p>
      </div>
    </div>
  );
};