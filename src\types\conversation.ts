export interface Character {
  id: string;
  name: string;
  role: string;
  personality: string;
  voiceId: string;
  color: 'alex' | 'jordan' | 'taylor' | 'user';
  avatar: string;
}

export interface ConversationMessage {
  id: string;
  character: Character;
  content: string;
  timestamp: Date;
  isUser?: boolean;
}

export interface ConversationState {
  isConnected: boolean;
  currentSpeaker: string | null;
  messages: ConversationMessage[];
  topic: string;
  participants: Character[];
}

export const CHARACTERS: Record<string, Character> = {
  alex: {
    id: 'alex',
    name: '<PERSON>',
    role: 'Optimistic Leader',
    personality: 'Enthusiastic, encouraging, sees opportunities in challenges',
    voiceId: 'EXAVITQu4vr4xnSDxMaL', // Sarah
    color: 'alex',
    avatar: '🌟'
  },
  jordan: {
    id: 'jordan',
    name: '<PERSON>',
    role: 'Skeptical Analyst',
    personality: 'Critical thinker, asks tough questions, data-driven',
    voiceId: 'CwhRBWXzGAHq8TQ4Fs17', // Roger
    color: 'jordan',
    avatar: '🔍'
  },
  taylor: {
    id: 'taylor',
    name: '<PERSON>',
    role: 'Creative Visionary',
    personality: 'Imaginative, thinks outside the box, innovative solutions',
    voiceId: 'XB0fDUnXU5powFXDhCwa', // Charlotte
    color: 'taylor',
    avatar: '🎨'
  }
};

export const USER_CHARACTER: Character = {
  id: 'user',
  name: 'You',
  role: 'Participant',
  personality: 'Engaged participant in the discussion',
  voiceId: '',
  color: 'user',
  avatar: '👤'
};