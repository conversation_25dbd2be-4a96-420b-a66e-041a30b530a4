import { useState, useCallback, useEffect } from 'react';
import { useConversation } from '@11labs/react';
import { ConversationMessage, ConversationState, CHARACTERS, USER_CHARACTER } from '@/types/conversation';
import { useToast } from '@/hooks/use-toast';

interface UseElevenLabsConversationProps {
  topic: string;
  apiKey?: string;
}

// Validate ElevenLabs API key format
const validateApiKey = (key: string): boolean => {
  // ElevenLabs API keys typically start with 'sk-' and have a specific length
  return key.length > 10 && (key.startsWith('sk-') || key.length > 20);
};

// Check microphone permissions
const checkMicrophonePermission = async (): Promise<boolean> => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.error('Microphone permission denied:', error);
    return false;
  }
};

export const useElevenLabsConversation = ({ topic, apiKey }: UseElevenLabsConversationProps) => {
  const [state, setState] = useState<ConversationState>({
    isConnected: false,
    currentSpeaker: null,
    messages: [],
    topic,
    participants: Object.values(CHARACTERS)
  });
  
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [isValidApiKey, setIsValidApiKey] = useState(false);
  const { toast } = useToast();

  // Validate API key when it changes
  useEffect(() => {
    if (apiKey) {
      setIsValidApiKey(validateApiKey(apiKey));
    } else {
      setIsValidApiKey(false);
    }
  }, [apiKey]);

  // Create system prompt for the conversation
  const createSystemPrompt = useCallback((topic: string) => {
    return `You are facilitating a group discussion about "${topic}". You embody three distinct AI characters:

1. **Alex** (Optimistic Leader): Enthusiastic, encouraging, sees opportunities in challenges. Always looks for positive solutions and motivates the group. Use an upbeat, energetic tone.

2. **Jordan** (Skeptical Analyst): Critical thinker who asks tough questions and demands evidence. Data-driven, challenges assumptions, plays devil's advocate. Use a thoughtful, questioning tone.

3. **Taylor** (Creative Visionary): Imaginative thinker who suggests innovative solutions and thinks outside the box. Brings fresh perspectives and creative approaches. Use an inspiring, imaginative tone.

IMPORTANT INSTRUCTIONS:
- Rotate between these three characters naturally in the conversation
- Each response should be from ONE character's perspective 
- Keep responses conversational and engaging (2-3 sentences max)
- Respond naturally to user input and build on their ideas
- Create a dynamic back-and-forth discussion
- Each character should have distinct viewpoints on the topic
- Wait for user responses and incorporate their input into the discussion

Start by having Alex introduce the topic and invite everyone to share their initial thoughts.`;
  }, []);

  const conversation = useConversation({
    onConnect: () => {
      setState(prev => ({ ...prev, isConnected: true }));
      toast({
        title: "Connected!",
        description: "Voice conversation is ready to begin."
      });
    },
    onDisconnect: () => {
      setState(prev => ({ ...prev, isConnected: false, currentSpeaker: null }));
      setIsRecording(false);
    },
    onMessage: (message) => {
      // Handle incoming messages from the AI
      // The message structure is { message: string, source: Role }
      if (message.source === 'ai' && message.message) {
        // Determine which character is speaking based on the message content
        const messageText = message.message;
        let character = CHARACTERS.alex; // Default to Alex
        
        // Simple character detection (in a real app, this would be more sophisticated)
        if (messageText.includes('skeptical') || messageText.includes('question') || messageText.includes('however')) {
          character = CHARACTERS.jordan;
        } else if (messageText.includes('creative') || messageText.includes('imagine') || messageText.includes('innovative')) {
          character = CHARACTERS.taylor;
        }

        const newMessage: ConversationMessage = {
          id: Date.now().toString(),
          character,
          content: messageText,
          timestamp: new Date(),
          isUser: false
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, newMessage],
          currentSpeaker: character.id
        }));
      } else if (message.source === 'user' && message.message) {
        // Handle user's transcribed speech
        const newMessage: ConversationMessage = {
          id: Date.now().toString(),
          character: USER_CHARACTER,
          content: message.message,
          timestamp: new Date(),
          isUser: true
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, newMessage]
        }));
      }
    },
    onError: (error) => {
      console.error('Conversation error:', error);
      toast({
        title: "Connection Error",
        description: "Failed to connect to voice service. Please check your API key and try again.",
        variant: "destructive"
      });
    },
    overrides: {
      agent: {
        prompt: {
          prompt: createSystemPrompt(topic)
        },
        firstMessage: `Hi everyone! I'm Alex, and I'm excited to discuss "${topic}" with you all. This is a fascinating subject with so many different angles to explore. Jordan, Taylor, and our human participant - what are your initial thoughts? What aspects of this topic interest you most?`,
        language: "en"
      }
    }
  });

  const requestMicrophonePermission = useCallback(async () => {
    try {
      const hasAccess = await checkMicrophonePermission();
      setHasPermission(hasAccess);
      
      if (hasAccess) {
        toast({
          title: "Microphone Ready",
          description: "Microphone access granted successfully."
        });
      } else {
        toast({
          title: "Microphone Access Denied",
          description: "Please allow microphone access to use voice features.",
          variant: "destructive"
        });
      }
      return hasAccess;
    } catch (error) {
      console.error('Microphone permission error:', error);
      toast({
        title: "Microphone Error",
        description: "Failed to access microphone. Please check your browser settings.",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const startConversation = useCallback(async () => {
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter and save your ElevenLabs API key first.",
        variant: "destructive"
      });
      return;
    }

    if (!isValidApiKey) {
      toast({
        title: "Invalid API Key",
        description: "Please check your ElevenLabs API key format.",
        variant: "destructive"
      });
      return;
    }

    // Check microphone permission first
    if (!hasPermission) {
      const hasAccess = await requestMicrophonePermission();
      if (!hasAccess) return;
    }

    try {
      // Mock connection for demo - in real app, you'd use ElevenLabs API
      // For now, we'll simulate a successful connection
      setState(prev => ({ ...prev, isConnected: true }));
      
      // Add welcome message
      const welcomeMessage: ConversationMessage = {
        id: Date.now().toString(),
        character: CHARACTERS.alex,
        content: `Hi everyone! I'm Alex, and I'm excited to discuss "${topic}" with you all. This is a fascinating subject with so many different angles to explore. What are your initial thoughts?`,
        timestamp: new Date(),
        isUser: false
      };
      
      setState(prev => ({ 
        ...prev, 
        messages: [welcomeMessage],
        isConnected: true 
      }));
      
      toast({
        title: "Connected!",
        description: "Discussion started! You can now speak with the AI characters."
      });
      
    } catch (error) {
      console.error('Failed to start conversation:', error);
      toast({
        title: "Connection Failed",
        description: "Failed to start conversation. Please try again.",
        variant: "destructive"
      });
    }
  }, [apiKey, isValidApiKey, hasPermission, topic, requestMicrophonePermission, toast]);

  const stopConversation = useCallback(async () => {
    try {
      await conversation.endSession();
    } catch (error) {
      console.log('Conversation end error (expected in demo):', error);
    }
    setState(prev => ({ 
      ...prev, 
      messages: [], 
      currentSpeaker: null, 
      isConnected: false 
    }));
    setIsRecording(false);
    toast({
      title: "Discussion Ended",
      description: "The conversation has been stopped."
    });
  }, [conversation, toast]);

  const toggleRecording = useCallback(() => {
    if (!state.isConnected) {
      toast({
        title: "Not Connected",
        description: "Please start the discussion first.",
        variant: "destructive"
      });
      return;
    }
    
    if (!hasPermission) {
      requestMicrophonePermission();
      return;
    }
    
    setIsRecording(prev => {
      const newState = !prev;
      if (newState && !isMuted) {
        // Simulate user speaking - in real app this would come from ElevenLabs
        setTimeout(() => {
          const responses = [
            "That's an interesting perspective! Jordan, what do you think about the analytical side of this?",
            "I have some concerns about that approach. Let me share some data that might change your view...",
            "What if we looked at this from a completely different angle? I'm thinking we could innovate here..."
          ];
          
          const randomResponse = responses[Math.floor(Math.random() * responses.length)];
          const character = Math.random() > 0.5 ? CHARACTERS.jordan : 
                          Math.random() > 0.5 ? CHARACTERS.taylor : CHARACTERS.alex;
          
          const aiResponse: ConversationMessage = {
            id: Date.now().toString(),
            character,
            content: randomResponse,
            timestamp: new Date(),
            isUser: false
          };
          
          setState(prev => ({ 
            ...prev, 
            messages: [...prev.messages, aiResponse],
            currentSpeaker: character.id
          }));
          
          // Stop speaking after 3 seconds
          setTimeout(() => {
            setState(prev => ({ ...prev, currentSpeaker: null }));
          }, 3000);
          
        }, 2000);
      }
      return newState;
    });
  }, [state.isConnected, hasPermission, isMuted, requestMicrophonePermission, toast]);

  const toggleMute = useCallback(() => {
    setIsMuted(prev => !prev);
  }, []);

  // Simulate AI speaking status (in real implementation, this would come from the conversation events)
  useEffect(() => {
    if (conversation.isSpeaking) {
      // Randomly assign which character is speaking (in production, this would be determined by the AI response)
      const characters = Object.keys(CHARACTERS);
      const randomCharacter = characters[Math.floor(Math.random() * characters.length)];
      setState(prev => ({ ...prev, currentSpeaker: randomCharacter }));
    } else {
      setState(prev => ({ ...prev, currentSpeaker: null }));
    }
  }, [conversation.isSpeaking]);

  return {
    state,
    isRecording,
    isMuted,
    hasPermission,
    isValidApiKey,
    startConversation,
    stopConversation,
    toggleRecording,
    toggleMute,
    requestMicrophonePermission,
    conversationStatus: conversation.status,
    isSpeaking: conversation.isSpeaking
  };
};