@tailwind base;
@tailwind components;
@tailwind utilities;

/* Voice Discussion Simulator Design System */

@layer base {
  :root {
    /* Base Colors - Dark Neumorphic Theme */
    --background: 240 8% 12%;
    --foreground: 240 5% 96%;
    
    /* Surface Colors */
    --surface: 240 8% 15%;
    --surface-elevated: 240 8% 18%;
    --surface-sunken: 240 8% 10%;
    
    /* Character Colors */
    --alex-primary: 200 100% 60%;      /* Optimistic Blue */
    --alex-secondary: 200 100% 70%;
    --alex-glow: 200 100% 60%;
    
    --jordan-primary: 30 100% 60%;     /* Analytical Orange */
    --jordan-secondary: 30 100% 70%;
    --jordan-glow: 30 100% 60%;
    
    --taylor-primary: 280 100% 60%;    /* Creative Purple */
    --taylor-secondary: 280 100% 70%;
    --taylor-glow: 280 100% 60%;
    
    --user-primary: 120 100% 60%;      /* User Green */
    --user-secondary: 120 100% 70%;
    --user-glow: 120 100% 60%;
    
    /* UI States */
    --speaking-glow: 0 0% 100%;
    --listening-pulse: 240 5% 30%;
    
    /* Shadows & Elevation */
    --shadow-neumorphic: 0 8px 16px rgba(0, 0, 0, 0.4), 0 -8px 16px rgba(255, 255, 255, 0.02);
    --shadow-elevated: 0 12px 24px rgba(0, 0, 0, 0.5), 0 -4px 12px rgba(255, 255, 255, 0.03);
    --shadow-sunken: inset 0 4px 8px rgba(0, 0, 0, 0.3), inset 0 -4px 8px rgba(255, 255, 255, 0.02);
    
    /* Gradients */
    --gradient-surface: linear-gradient(145deg, hsl(240 8% 18%), hsl(240 8% 12%));
    --gradient-button: linear-gradient(145deg, hsl(240 8% 20%), hsl(240 8% 16%));
    --gradient-glow: radial-gradient(circle, transparent, hsl(var(--speaking-glow) / 0.1));
    
    /* Legacy shadcn variables */
    --card: var(--surface);
    --card-foreground: var(--foreground);
    --popover: var(--surface-elevated);
    --popover-foreground: var(--foreground);
    --primary: var(--alex-primary);
    --primary-foreground: 240 8% 12%;
    --secondary: var(--surface-elevated);
    --secondary-foreground: var(--foreground);
    --muted: var(--surface-sunken);
    --muted-foreground: 240 5% 60%;
    --accent: var(--surface-elevated);
    --accent-foreground: var(--foreground);
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 8% 20%;
    --input: 240 8% 18%;
    --ring: var(--alex-primary);
    --radius: 1rem;

    /* Sidebar (kept for compatibility) */
    --sidebar-background: var(--surface);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--alex-primary);
    --sidebar-primary-foreground: var(--background);
    --sidebar-accent: var(--surface-elevated);
    --sidebar-accent-foreground: var(--foreground);
    --sidebar-border: 240 8% 20%;
    --sidebar-ring: var(--alex-primary);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    background: linear-gradient(145deg, hsl(240 8% 12%), hsl(240 8% 10%));
    min-height: 100vh;
  }
}

@layer components {
  /* Neumorphic Surface */
  .neumorphic {
    background: var(--gradient-surface);
    box-shadow: var(--shadow-neumorphic);
    border: 1px solid hsl(240 8% 20% / 0.5);
  }
  
  .neumorphic-elevated {
    background: var(--gradient-surface);
    box-shadow: var(--shadow-elevated);
    border: 1px solid hsl(240 8% 22% / 0.5);
  }
  
  .neumorphic-sunken {
    background: hsl(var(--surface-sunken));
    box-shadow: var(--shadow-sunken);
    border: 1px solid hsl(240 8% 8% / 0.8);
  }
  
  /* Character Avatars */
  .avatar-alex {
    background: linear-gradient(145deg, hsl(var(--alex-primary)), hsl(var(--alex-secondary)));
    box-shadow: 0 0 20px hsl(var(--alex-glow) / 0.3);
  }
  
  .avatar-jordan {
    background: linear-gradient(145deg, hsl(var(--jordan-primary)), hsl(var(--jordan-secondary)));
    box-shadow: 0 0 20px hsl(var(--jordan-glow) / 0.3);
  }
  
  .avatar-taylor {
    background: linear-gradient(145deg, hsl(var(--taylor-primary)), hsl(var(--taylor-secondary)));
    box-shadow: 0 0 20px hsl(var(--taylor-glow) / 0.3);
  }
  
  .avatar-user {
    background: linear-gradient(145deg, hsl(var(--user-primary)), hsl(var(--user-secondary)));
    box-shadow: 0 0 20px hsl(var(--user-glow) / 0.3);
  }
  
  /* Speaking Animation */
  .speaking-pulse {
    animation: speaking-pulse 1.5s ease-in-out infinite;
  }
  
  .listening-pulse {
    animation: listening-pulse 2s ease-in-out infinite;
  }
  
  /* Voice Activity Indicator */
  .voice-wave {
    animation: voice-wave 0.8s ease-in-out infinite alternate;
  }
}

@layer utilities {
  .text-gradient-alex {
    background: linear-gradient(90deg, hsl(var(--alex-primary)), hsl(var(--alex-secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-jordan {
    background: linear-gradient(90deg, hsl(var(--jordan-primary)), hsl(var(--jordan-secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-taylor {
    background: linear-gradient(90deg, hsl(var(--taylor-primary)), hsl(var(--taylor-secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-user {
    background: linear-gradient(90deg, hsl(var(--user-primary)), hsl(var(--user-secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Keyframe Animations */
@keyframes speaking-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 20px hsl(var(--speaking-glow) / 0.4);
  }
  50% { 
    transform: scale(1.1);
    box-shadow: 0 0 30px hsl(var(--speaking-glow) / 0.6);
  }
}

@keyframes listening-pulse {
  0%, 100% { 
    opacity: 0.6;
  }
  50% { 
    opacity: 1;
  }
}

@keyframes voice-wave {
  0% { transform: scaleY(1); }
  100% { transform: scaleY(1.5); }
}

@keyframes fade-in {
  0% { 
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-in-right {
  0% { 
    opacity: 0;
    transform: translateX(30px);
  }
  100% { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  0% { 
    opacity: 0;
    transform: translateX(-30px);
  }
  100% { 
    opacity: 1;
    transform: translateX(0);
  }
}